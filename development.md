## Overview

We follow a consistent structure for form handling, server actions, and client interactions.
This document outlines **best practices**, **code snippets**, and **usage conventions** to keep the codebase clean and scalable.

---

## 📂 Project Conventions

### Forms

- Use `react-hook-form` + `zod` for validation.
- Always import input elements from `@/components/Field`. Create a custom `Field` if necessary.
- Use `tf` wrapper for server actions to handle errors gracefully.
- Show success/failure messages with `sonner`’s `toast`.
- Ensure all labels, placeholders, and messages are internationalized via `t("...")`.

### Example Form Snippet

```tsx
import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { Field } from "@/components/Field";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { tf } from "@/lib/tf";
import z from "zod";
import { onSave } from "./ExampleForm.action";

const formSchema = z.object({
  name: z.string().nonempty(),
  email: z.string().email(),
});

export function ExampleForm({ userData }: { userData: { name: string; email: string } }) {
  const t = useTranslations();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: userData.name,
      email: userData.email,
    },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsLoading(true);
    const res = await tf(t, onSave, values);
    if (res) {
      toast.success(t("Form action successful!"));
    }
    setIsLoading(false);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Field name="name" label={t("Full Name")} control={form.control} />
        <Field name="email" label={t("Email Address")} control={form.control} input={{ props: { type: "email" } }} />
        <Button type="submit" disabled={isLoading} loading={isLoading}>
          {t("Submit")}
        </Button>
      </form>
    </Form>
  );
}
```

---

### Pages

- Use src/components/Heading.tsx for heading
- Use src/components/Filter-Pagination.tsx for pagination

---

## 🧩 Server Actions

- Keep all server actions colocated with the feature in a `.action.ts` file.
- Always return a structured response (success/error).
- Wrap client calls with `tf` for safe handling.

```ts
// ExampleForm.action.ts
"use server";

import { errors } from "@/lib/errors";
import { getDataContext } from "@/lib/getDataContext";

export async function onSave(values: { name: string; email: string }) {
  const context = await getDataContext();

  if (!context.session?.user) return { error: errors.NotAuthenticated };

  return await connectToAccount(context.db, context.session?.user, socialPlatform);
}
```

---

## 🚨 Error Handling

The user-facing errors need to be **minimal and reusable**.
Define them in a central enum at `src/lib/errors.ts`:

```ts
// src/lib/errors.ts
export enum errors {
  NotAuthenticated = "Not authenticated",
  NotAuthorized = "Not authorized",
  NoConnectedSocialAccount = "No social accounts connected",
  InternalServerError = "Internal server error",
  // ...
}
```

### Rules

- **Only use these errors in server actions** (e.g., `.action.ts` files).
- Internal backend services do **not** need to use this enum; they can `console.error` or log detailed messages as needed.
- On the client side, errors are already handled by `tf`, so you should **not** import or reference the `errors` enum in React components.

This ensures:

- End-user messages remain minimal and consistent.
- Developers keep detailed internal errors out of the UI.
- All user-facing error text is managed in one place (`src/lib/errors.ts`).

---

## 🗣️ Internationalization (i18n)

- Use `next-intl` for all UI strings.
- No hardcoded text allowed in forms, buttons, or error messages.

✅ Good

```tsx
<Field name="name" label={t("Full Name")} control={form.control} />
```

❌ Bad

```tsx
<Field name="name" label="Full Name" control={form.control} />
```

### Ternary Operator

Do not use ternary operator inside t function

✅ Good

```tsx
<Button>{isEditing ? t("Update") : t("Create")}</Button>
```

❌ Bad

```tsx
<Button>{t(isEditing ? "Update" : "Create")}</Button>
```

### RSC

For react server components use `T` component.

```tsx
<T text="Full Name" />
```

For react client components use `t` hook.

```tsx
const t = useTranslations();
```

---

## 🗄️ Database Queries

All Drizzle database queries should be organized in dedicated query files within `src/database/drizzle/queries/*.ts` and imported from there. **Never write manual database queries directly in your components, actions, or other application code.**

### Query Organization

- **Location**: All queries must be in `src/database/drizzle/queries/`
- **File naming**: Use descriptive names like `packages.ts`, `users.ts`, `subscriptions.ts`
- **Function naming**: Use clear, descriptive function names like `getAllPackages`, `getUserById`, `insertPackage`

### Example Query File Structure

```ts
// src/database/drizzle/queries/packages.ts
import { desc, eq } from "drizzle-orm";
import { PackageInsert, PackageItem, packageTable } from "../schema/packages";

export function getAllPackages(db: DB) {
  return db.select().from(packageTable).where(eq(packageTable.isActive, true)).orderBy(desc(packageTable.priority));
}

export function getPackageById(db: DB, id: string) {
  return db.select().from(packageTable).where(eq(packageTable.id, id)).limit(1);
}

export function insertPackage(db: DB, data: PackageInsert) {
  return db.insert(packageTable).values(data).returning();
}

export function updatePackage(db: DB, id: string, data: Partial<PackageItem>) {
  return db
    .update(packageTable)
    .set({
      ...data,
      updatedAt: new Date(),
    })
    .where(eq(packageTable.id, id))
    .returning();
}
```

### Usage in Actions

```ts
// ✅ Good - Import from queries
import * as packageQueries from "@/database/drizzle/queries/packages";

export async function onCreatePackage(data: CreatePackageData) {
  const { db, session } = await getDataContext();

  if (!session?.user) return { error: errors.NotAuthenticated };

  const newPackage = await packageQueries.insertPackage(db, data);
  return { success: true, package: newPackage[0] };
}
```

```ts
// ❌ Bad - Manual queries in actions
export async function onCreatePackage(data: CreatePackageData) {
  const { db, session } = await getDataContext();

  if (!session?.user) return { error: errors.NotAuthenticated };

  // Don't do this - write queries directly in actions
  const newPackage = await db.insert(packageTable).values(data).returning();
  return { success: true, package: newPackage[0] };
}
```

### Benefits

- **Reusability**: Queries can be reused across different parts of the application
- **Maintainability**: Changes to queries only need to be made in one place
- **Testing**: Query functions can be easily unit tested
- **Type Safety**: Centralized query functions provide better TypeScript support
- **Consistency**: Ensures consistent query patterns across the codebase

---

## ✅ Checklist Before Commit

- [ ] All forms use `Field` from `@/components/Field`.
- [ ] All validation schemas use `zod`. Use nonempty() for required fields.
- [ ] All text is internationalized (`t("...")`).
- [ ] Server actions live in `.action.ts`.
- [ ] API calls wrapped in `tf`.
- [ ] UI shows feedback (`toast.success` / `toast.error`).
- [ ] **Server function errors are defined in `src/lib/errors.ts` and never hardcoded.**
- [ ] **All database queries are in `src/database/drizzle/queries/*.ts` and imported from there.**

---
