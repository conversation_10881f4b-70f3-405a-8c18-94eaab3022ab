import { dbInit } from "@/database/drizzle/db";
import { getAllTranslations } from "@/database/drizzle/queries/translations";
import { getRequestConfig } from "next-intl/server";
import { cookies } from "next/headers";

export default getRequestConfig(async () => {
  const serverCookies = await cookies();
  const locale = serverCookies.get("locale")?.value || "en";
  const db = dbInit();
  const messages = await getAllTranslations(db);

  return {
    locale,
    messages: messages[locale].translation,
  };
});
