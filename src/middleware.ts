import { getDataContext } from "@/lib/getDataContext";
import { getUserSubscriptionStatus } from "@/lib/subscription-service";
import { getSessionCookie } from "better-auth/cookies";
import { NextRequest, NextResponse } from "next/server";
import { getOrCreateProfile } from "./lib/getlate";

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const ignoredPrefixes = [
    //
    "/api",
    "/_next",
    "/webhooks",
  ];

  if (!ignoredPrefixes.some((prefix) => pathname.startsWith(prefix))) {
    const sessionCookie = getSessionCookie(request);

    if (!sessionCookie) {
      const publicPages = [
        //
        "/login",
        "/",
      ];

      if (!publicPages.includes(pathname) && !/^\/videos\/[\w-]+\/?$/.test(pathname)) {
        return NextResponse.redirect(new URL("/login", process.env.BASE_URL));
      }
    } else {
      if (pathname === "/login") {
        return NextResponse.redirect(new URL("/", process.env.BASE_URL));
      }
      if (pathname === "/notifications") {
        return NextResponse.next();
      }

      const { db, session } = await getDataContext();

      if (session?.user?.role === "admin") {
        if (pathname.startsWith("/admin")) {
          return NextResponse.next();
        } else {
          return NextResponse.redirect(new URL("/admin", process.env.BASE_URL));
        }
      }

      const freePages = [
        //
        "/packages",
        "/account/profile",
      ];

      if (!freePages.includes(pathname)) {
        if (session?.user) {
          const subscriptionStatus = await getUserSubscriptionStatus(db, session.user.id);

          // If user doesn't have an active subscription, redirect to packages page
          if (!subscriptionStatus.hasActiveSubscription) {
            return NextResponse.redirect(new URL("/packages?required=true", process.env.BASE_URL));
          } else if (["/", "/videos"].includes(pathname)) {
            const getLateProfile = await getOrCreateProfile(db, session.user);
            if (getLateProfile?.accounts.length === 0) {
              return NextResponse.redirect(new URL("/account/social-accounts?required=true", process.env.BASE_URL));
            }
          }
        }
      }
    }
  }

  return NextResponse.next();
}

export const config = {
  runtime: "nodejs",
};
