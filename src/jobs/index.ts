import { dbInit } from "@/database/drizzle/db";
import { updateJob } from "@/database/drizzle/queries/jobs";
import "dotenv/config";
import { WGenerateVideo } from "./workers/generateVideo";

const workers = [WGenerateVideo()];

console.log(`${workers.length} workers started.`);

for (const worker of workers) {
  worker?.on("completed", async (job, result) => {
    const db = dbInit();
    console.log(`Job ${job.id} completed. Result:`, result);
    await updateJob(db, job.id!, { status: "completed", endedAt: new Date() });
  });

  worker?.on("error", async (err) => {
    // log the error
    console.error(err);
  });
}
