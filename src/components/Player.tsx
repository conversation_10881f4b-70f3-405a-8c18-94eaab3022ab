import { cn } from "@/lib/utils";
import { Volume2Icon } from "lucide-react";
import {
  MediaControlBar,
  MediaController,
  MediaFullscreenButton,
  MediaMuteButton,
  MediaPlaybackRateButton,
  MediaPlayButton,
  MediaSeekBackwardButton,
  MediaSeekForwardButton,
  MediaTimeDisplay,
  MediaTimeRange,
  MediaVolumeRange,
} from "media-chrome/react";
import React, { ComponentProps, useState } from "react";
import ReactPlayer from "react-player";
import { AUDIO_EXTENSIONS, canPlay } from "react-player/patterns";
import type { VideoElementProps } from "react-player/types";
import { Button } from "./ui/button";

const HtmlPlayer = React.forwardRef<HTMLVideoElement, VideoElementProps>(function HtmlPlayer(props, ref) {
  const Media = AUDIO_EXTENSIONS.test(`${props.src}`) ? "audio" : "video";

  return (
    <Media {...props} ref={ref} tabIndex={-1}>
      {props.children}
    </Media>
  );
});

const customPlayer = {
  key: "html",
  name: "html",
  canPlay: canPlay.html,
  canEnablePIP: () => true,
  player: HtmlPlayer,
};

ReactPlayer.addCustomPlayer?.(customPlayer);

const controlStyle = cn("px-3 py-2");

export default function Player(props: ComponentProps<typeof ReactPlayer>) {
  const [showVol, setShowVol] = useState(false);

  return (
    <MediaController
      style={{
        width: "100%",
        aspectRatio: "9/16",
        "--media-text-color": "var(--secondary-foreground)",
        "--media-primary-color": "var(--primary)",
        "--media-secondary-color": "var(--secondary)",
        "--media-control-hover-background": "color-mix(in oklab, var(--secondary) 90%, #000000)",
        "--media-icon-color": "var(--secondary-foreground)",
        "--media-range-track-background": "var(--secondary-foreground)",
        "--media-menu-hidden-max-height": "0",
      }}
    >
      <ReactPlayer
        slot="media"
        controls={false}
        style={{
          width: "100%",
          height: "100%",
          "--controls": "none",
        }}
        {...props}
      ></ReactPlayer>
      <MediaTimeRange
        style={{
          "--media-control-height": "0",
          "--media-range-bar-color": "red",
          "--media-range-thumb-background": "red",
          "--_media-range-padding": "0",
          zIndex: 100,
          width: "100%",
        }}
      />
      <MediaControlBar className="justify-center bg-secondary">
        <MediaPlayButton className={controlStyle} />
        <MediaSeekBackwardButton seekOffset={10} className={controlStyle} />
        <MediaSeekForwardButton seekOffset={10} className={controlStyle} />
        <MediaTimeDisplay showDuration className={controlStyle} />
        <div className="relative">
          <Button variant="ghost" size="icon" onClick={() => setShowVol((showVol) => !showVol)}>
            <Volume2Icon className="size-6" />
          </Button>
          <div className={cn("bottom-full absolute flex right-0", !showVol && "hidden")}>
            <MediaMuteButton className={controlStyle} />
            <MediaVolumeRange className={controlStyle} />
          </div>
        </div>
        <MediaPlaybackRateButton className={controlStyle} />
        <MediaFullscreenButton className={controlStyle} />
      </MediaControlBar>
    </MediaController>
  );
}
