"use client";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { Eye, EyeOff } from "lucide-react";
import { useTranslations } from "next-intl";
import { ComponentProps, ReactNode, useState } from "react";
import { ControllerProps, FieldPath, FieldValues, useController } from "react-hook-form";
import { DateTimePicker } from "./DateTimePicker";
import { Button } from "./ui/button";
import { Checkbox } from "./ui/checkbox";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "./ui/form";
import { Input } from "./ui/input";
import MultipleSelector from "./ui/multiple-selector";
import { Textarea } from "./ui/textarea";

export function Field<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  label,
  input,
  ...props
}: Omit<ControllerProps<TFieldValues, TName>, "render"> & {
  label: string;
  input?: {
    props?: Omit<ComponentProps<typeof Input>, "ref">;
  };
}) {
  return (
    <FormField
      {...props}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <Input placeholder={label} {...field} {...input?.props} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export function PasswordField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  label,
  input,
  ...props
}: Omit<ControllerProps<TFieldValues, TName>, "render"> & {
  label: string;
  input?: {
    props?: Omit<ComponentProps<typeof Input>, "ref">;
  };
}) {
  const [show, setShow] = useState(false);

  return (
    <FormField
      {...props}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <div className="relative">
            <FormControl>
              <Input placeholder={label} {...field} {...input?.props} type={show ? "text" : "password"} />
            </FormControl>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
              onClick={() => setShow((show) => !show)}
            >
              {show ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </Button>
          </div>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export function TextareaField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  label,
  input,
  ...props
}: Omit<ControllerProps<TFieldValues, TName>, "render"> & {
  label: string;
  input?: {
    props?: Omit<ComponentProps<typeof Textarea>, "ref">;
  };
}) {
  return (
    <FormField
      {...props}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <Textarea placeholder={label} {...field} {...input?.props} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export function RichField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  label,
  // input,
  ...props
}: Omit<ControllerProps<TFieldValues, TName>, "render"> & {
  label: string;
  input?: {
    props?: Omit<ComponentProps<"div">, "ref">;
  };
}) {
  const { field } = useController({
    control: props.control,
    name: props.name,
  });
  const editor = useEditor({
    extensions: [StarterKit], // define your extension array
    content: field.value,
    onUpdate: (event) => {
      field.onChange(event.editor.getHTML());
    },
    immediatelyRender: false,
  });
  return (
    <FormField
      {...props}
      render={(
        {
          // field
        },
      ) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <EditorContent editor={editor} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export function SelectField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  label,
  options,
  ...props
}: Omit<ControllerProps<TFieldValues, TName>, "render"> & { label: string; options: { label: string; value: any }[] }) {
  return (
    <FormField
      {...props}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <Select {...field} onValueChange={field.onChange}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder={label} />
              </SelectTrigger>
              <SelectContent>
                {options.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export function MultipleSelectorField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  label,
  inputProps,
  ...props
}: Omit<ControllerProps<TFieldValues, TName>, "render"> & {
  label: string;
  inputProps: ComponentProps<typeof MultipleSelector>;
}) {
  const t = useTranslations();
  const options = inputProps.defaultOptions || inputProps.options || [];
  const map = new Map(options.map((x) => [x.value, x]));

  return (
    <FormField
      {...props}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <MultipleSelector
              value={field.value.map((x: string) => map.get(x))}
              onChange={(options) => field.onChange(options.map((x) => x.value))}
              placeholder={t("Select options")}
              emptyIndicator={t("No options left to select")}
              {...inputProps}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export function DateTimeField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  label,
  inputProps,
  ...props
}: Omit<ControllerProps<TFieldValues, TName>, "render"> & {
  label: string;
  inputProps?: ComponentProps<typeof DateTimePicker>;
}) {
  return (
    <FormField
      {...props}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <DateTimePicker {...inputProps} value={field.value} onChange={field.onChange} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export function CheckboxField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({ label, ...props }: Omit<ControllerProps<TFieldValues, TName>, "render"> & { label: ReactNode }) {
  return (
    <FormField
      {...props}
      render={({ field }) => (
        <FormItem className="flex flex-row items-center gap-2">
          <FormControl>
            <Checkbox {...field} checked={field.value} onCheckedChange={field.onChange} />
          </FormControl>
          <FormLabel className="text-sm font-normal">{label}</FormLabel>
        </FormItem>
      )}
    />
  );
}
