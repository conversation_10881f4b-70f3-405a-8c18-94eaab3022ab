import { dbInit } from "@/database/drizzle/db";
import { createOrEditFeature } from "@/database/drizzle/queries/features";
import { getAllPackages, insertPackage } from "@/database/drizzle/queries/packages";
import { PackageInsert, PackageItem } from "@/database/drizzle/schema/packages";
import { Feature } from "@/lib/features";
import "dotenv/config";

const defaultPackages: (PackageInsert & { features: Feature[] })[] = [
  {
    name: "Starter",
    description: "Great for content creators and small businesses",
    price: "9.99",
    currency: "eur",
    billingInterval: "month",
    isActive: true,
    maxVideos: 25,
    maxStorageGB: 5,
    priority: 2,
    features: [
      { type: "Premium templates", meta: {} },
      { type: "Advanced analytics", meta: {} },
    ],
  },
  {
    name: "Starter",
    description: "Great for content creators and small businesses",
    price: "95.90",
    currency: "eur",
    billingInterval: "year",
    isActive: true,
    maxVideos: 25,
    maxStorageGB: 5,
    priority: 2,
    features: [
      { type: "Premium templates", meta: {} },
      { type: "Advanced analytics", meta: {} },
    ],
  },
  {
    name: "Pro",
    description: "Perfect for growing businesses and agencies",
    price: "29.99",
    currency: "eur",
    billingInterval: "month",
    isActive: true,
    maxVideos: 100,
    maxStorageGB: 20,
    priority: 3,
    features: [
      { type: "Premium templates", meta: {} },
      { type: "Advanced analytics", meta: {} },
      { type: "24/7 priority support", meta: { responseTime: 4, channels: ["chat", "email"] } },
      { type: "Team collaboration", meta: { maxMembers: 5, roles: ["admin", "editor", "viewer"] } },
    ],
  },
  {
    name: "Enterprise",
    description: "Unlimited power for large organizations",
    price: "99.99",
    currency: "eur",
    billingInterval: "month",
    isActive: true,
    maxVideos: null, // unlimited
    maxStorageGB: null, // unlimited
    priority: 4,
    features: [
      { type: "Premium templates", meta: {} },
      { type: "Advanced analytics", meta: {} },
      { type: "24/7 priority support", meta: { responseTime: 1, channels: ["chat", "email", "phone"] } },
      { type: "Team collaboration", meta: { maxMembers: 50, roles: ["admin", "editor", "viewer"] } },
      { type: "API access", meta: { rateLimit: 1000 } },
      { type: "Custom integrations", meta: {} },
    ],
  },
  {
    name: "Pro",
    description: "Pro plan with annual billing - save 20%",
    price: "287.88", // $29.99 * 12 * 0.8 = $287.88 (20% discount)
    currency: "eur",
    billingInterval: "year",
    isActive: true,
    maxVideos: 100,
    maxStorageGB: 20,
    priority: 5,
    features: [
      { type: "Premium templates", meta: {} },
      { type: "Advanced analytics", meta: {} },
      { type: "24/7 priority support", meta: { responseTime: 4, channels: ["chat", "email"] } },
      { type: "Team collaboration", meta: { maxMembers: 5, roles: ["admin", "editor", "viewer"] } },
    ],
  },
  {
    name: "Enterprise",
    description: "Enterprise plan with annual billing - save 20%",
    price: "959.88", // $99.99 * 12 * 0.8 = $959.88 (20% discount)
    currency: "eur",
    billingInterval: "year",
    isActive: true,
    maxVideos: null, // unlimited
    maxStorageGB: null, // unlimited
    priority: 6,
    features: [
      { type: "Premium templates", meta: {} },
      { type: "Advanced analytics", meta: {} },
      { type: "24/7 priority support", meta: { responseTime: 1, channels: ["chat", "email", "phone"] } },
      { type: "Team collaboration", meta: { maxMembers: 50, roles: ["admin", "editor", "viewer"] } },
      { type: "API access", meta: { rateLimit: 1000 } },
      { type: "Custom integrations", meta: {} },
    ],
  },
];

async function seedPackages() {
  console.log("🌱 Seeding packages...");

  if (!process.env.DATABASE_URL) {
    console.error("❌ DATABASE_URL environment variable is not set");
    process.exit(1);
  }

  console.log(`📡 Connecting to database: ${process.env.DATABASE_URL.replace(/:[^:@]*@/, ":***@")}`);

  const db = dbInit();

  try {
    // Check if packages already exist
    const existingPackages = await getAllPackages(db);
    if (existingPackages.length > 0) {
      console.log(`⚠️  Found ${existingPackages.length} existing packages.`);

      for (const { features, ...packageData } of defaultPackages) {
        let pkg = existingPackages.find(
          (p) => p.name === packageData.name && p.billingInterval === packageData.billingInterval,
        ) as PackageItem | undefined;
        if (!pkg) {
          console.log(`Creating package: ${packageData.name}`);
          const [newPackage] = await insertPackage(db, packageData);
          pkg = newPackage;
        }
        if (pkg && features.length > 0) {
          console.log(`  Adding ${features.length} features to ${packageData.name}`);
          for (const feature of features) {
            await createOrEditFeature(db, feature.type, pkg.id, {
              meta: feature.meta,
            });
          }
        }
      }
      return;
    }

    for (const { features, ...packageData } of defaultPackages) {
      console.log(`Creating package: ${packageData.name}`);
      const [newPackage] = await insertPackage(db, packageData);

      if (newPackage && features.length > 0) {
        console.log(`  Adding ${features.length} features to ${packageData.name}`);
        for (const feature of features) {
          await createOrEditFeature(db, feature.type, newPackage.id, {
            meta: feature.meta,
          });
        }
      }
    }

    console.log("✅ Packages seeded successfully!");
  } catch (error) {
    console.error("❌ Error seeding packages:", error);

    const errorMessage = error instanceof Error ? error.message : String(error);
    if (errorMessage.includes("ENOTFOUND") || errorMessage.includes("getaddrinfo")) {
      console.error("💡 Database connection failed. Make sure:");
      console.error("   1. Database server is running");
      console.error("   2. DATABASE_URL is correct");
      console.error("   3. If using Docker: run 'docker-compose up -d db' first");
      console.error("   4. If using local DB: update DATABASE_URL to point to localhost");
    }

    process.exit(1);
  }
}

// Run the seed function if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  seedPackages();
}

export { defaultPackages, seedPackages };
