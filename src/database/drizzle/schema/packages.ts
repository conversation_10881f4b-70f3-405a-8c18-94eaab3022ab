import { FeatureMeta, FeatureType } from "@/lib/features";
import { relations } from "drizzle-orm";
import { boolean, decimal, integer, jsonb, pgTable, primaryKey, text, timestamp, uuid } from "drizzle-orm/pg-core";
import { user } from "./auth";

export const packageTable = pgTable("packages", {
  id: uuid().primaryKey().defaultRandom(),
  name: text().notNull(),
  description: text(),
  price: decimal({ precision: 10, scale: 2 }).notNull(), // Price in dollars
  currency: text().default("eur").notNull(),
  billingInterval: text({ enum: ["month", "year"] }).notNull(),
  isActive: boolean().default(true).notNull(),
  stripePriceId: text(), // Stripe Price ID
  stripeProductId: text(), // Stripe Product ID
  maxVideos: integer(), // Max videos per billing period (null = unlimited)
  maxStorageGB: integer(), // Max storage in GB (null = unlimited)
  priority: integer().default(0).notNull(), // For ordering packages
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .$onUpdate(() => /* @__PURE__ */ new Date())
    .notNull(),
});

export type PackageItem = typeof packageTable.$inferSelect;
export type PackageInsert = typeof packageTable.$inferInsert;

export const featureTable = pgTable(
  "features",
  {
    type: text().notNull().$type<FeatureType>(),
    packageId: uuid()
      .notNull()
      .references(() => packageTable.id, { onDelete: "cascade" }),
    meta: jsonb().$type<FeatureMeta<FeatureType>>().default({}),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at")
      .defaultNow()
      .$onUpdate(() => /* @__PURE__ */ new Date())
      .notNull(),
  },
  (table) => [primaryKey({ columns: [table.type, table.packageId] })],
);

export type FeatureItem = typeof featureTable.$inferSelect;
export type FeatureInsert = typeof featureTable.$inferInsert;

// Relations will be defined after both tables are created

export const subscriptionTable = pgTable("subscriptions", {
  id: uuid().primaryKey().defaultRandom(),
  userId: text()
    .notNull()
    .references(() => user.id, { onDelete: "cascade" }),
  packageId: uuid()
    .notNull()
    .references(() => packageTable.id, { onDelete: "cascade" }),
  stripeSubscriptionId: text().unique(), // Stripe Subscription ID
  stripeCustomerId: text(), // Stripe Customer ID
  status: text({
    enum: ["active", "canceled", "incomplete", "incomplete_expired", "past_due", "trialing", "unpaid"],
  }).notNull(),
  currentPeriodStart: timestamp(),
  currentPeriodEnd: timestamp(),
  cancelAtPeriodEnd: boolean().default(false).notNull(),
  canceledAt: timestamp(),
  trialStart: timestamp(),
  trialEnd: timestamp(),
  metadata: jsonb().$type<Record<string, any>>().default({}),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .$onUpdate(() => /* @__PURE__ */ new Date())
    .notNull(),
});

export type SubscriptionItem = typeof subscriptionTable.$inferSelect;
export type SubscriptionInsert = typeof subscriptionTable.$inferInsert;

export const subscriptionRelations = relations(subscriptionTable, ({ one }) => ({
  user: one(user, {
    fields: [subscriptionTable.userId],
    references: [user.id],
  }),
  package: one(packageTable, {
    fields: [subscriptionTable.packageId],
    references: [packageTable.id],
  }),
}));

// Define relations after both tables are created
export const packageRelations = relations(packageTable, ({ many }) => ({
  subscriptions: many(subscriptionTable),
  features: many(featureTable),
}));

// Add relations to user table
export const userRelations = relations(user, ({ many }) => ({
  subscriptions: many(subscriptionTable),
}));

export const featureRelations = relations(featureTable, ({ one }) => ({
  package: one(packageTable, {
    fields: [featureTable.packageId],
    references: [packageTable.id],
  }),
}));
