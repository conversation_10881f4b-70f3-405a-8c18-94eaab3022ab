"use server";

import { getVideo } from "@/database/drizzle/queries/videos";
import { getVoices } from "@/lib/elevenlabs";
import { getDataContext } from "@/lib/getDataContext";
import { redirect } from "next/navigation";

export type Data = Awaited<ReturnType<typeof getData>>;

export const getData = async ({ params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params;
  const { db, session } = await getDataContext();
  const [video] = await getVideo(db, id);
  const voices = await getVoices();

  // If video is private and user is not the owner, redirect to login or videos page
  if (video?.visibility === "private") {
    if (!session?.user) {
      redirect("/login");
    } else if (video.userId !== session.user.id) {
      redirect("/videos");
    }
  }

  return { video, voices, session };
};
