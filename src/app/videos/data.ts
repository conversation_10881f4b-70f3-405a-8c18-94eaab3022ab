"use server";

import * as drizzleQueries from "@/database/drizzle/queries/videos";
import { getDataContext } from "@/lib/getDataContext";
import { getOrCreateProfile } from "@/lib/getlate";

export type Data = Awaited<ReturnType<typeof getData>>;

export async function getData() {
  const { db, session } = await getDataContext();
  if (!session?.user) throw new Error("Not authenticated");
  const videos = await drizzleQueries.getAllVideos(db, session.user.id);
  const getlateProfile = await getOrCreateProfile(db, session.user);

  return { videos, getlateProfile };
}
