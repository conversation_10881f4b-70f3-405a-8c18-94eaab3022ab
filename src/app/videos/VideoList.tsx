"use client";

import { <PERSON><PERSON><PERSON><PERSON>, DateTimeField } from "@/components/Field";
import Player from "@/components/Player";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>H<PERSON>er, <PERSON><PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import { Form } from "@/components/ui/form";
import { VideoItem } from "@/database/drizzle/schema/videos";
import { tf } from "@/lib/tf";
import { zodResolver } from "@hookform/resolvers/zod";
import { EditIcon, ShareIcon, VideoOffIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import z from "zod";
import { platforms } from "../account/social-accounts/platforms";
import { onShare } from "./VideoList.action";
import { useData } from "./data.client";

export function VideoList({ initialVideoItems }: { initialVideoItems: VideoItem[] }) {
  const t = useTranslations();
  const [videoItems] = useState(initialVideoItems);

  return (
    <>
      <h1 className="mb-4 text-2xl font-bold">{t("Videos")}</h1>
      <div className="grid grid-cols-[repeat(auto-fill,minmax(300px,1fr))] gap-6">
        {videoItems.map((videoItem) => (
          <div key={videoItem.id} className="rounded-2xl overflow-clip">
            {videoItem.videoUrl ? (
              <Player src={videoItem.videoUrl!} className="aspect-[9/16] bg-gray-50"></Player>
            ) : (
              <div className="aspect-[9/16] bg-gray-50 flex items-center justify-center">
                <VideoOffIcon className="h-16 w-16 text-gray-500" />
              </div>
            )}
            <div className="p-3 flex gap-3 items-center">
              <Link href={`/videos/${videoItem.id}/`}>{videoItem.target}</Link>
              <Button asChild className="ms-auto">
                <Link href={`/videos/${videoItem.id}/edit`}>
                  <EditIcon /> <span className="sr-only">{t("Edit")}</span>
                </Link>
              </Button>
              <ShareDialog videoId={videoItem.id} />
            </div>
          </div>
        ))}
      </div>
    </>
  );
}

const formSchema = z.object({
  platforms: z.array(z.boolean()).length(platforms.length),
  scheduleAt: z.date(),
});

function ShareDialog({ videoId }: { videoId: string }) {
  const t = useTranslations();
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      platforms: platforms.map(() => false),
      scheduleAt: new Date(),
    },
  });
  const { getlateProfile } = useData();
  const [postNow, setPostNow] = useState<"indeterminate" | boolean>(true);
  const [isSharing, setIsSharing] = useState(false);
  const accountsMap = useMemo(() => {
    return new Map((getlateProfile?.accounts || []).map((x) => [x.platform.toLowerCase(), x]));
  }, [getlateProfile?.accounts]);

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button>
          <ShareIcon />
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t("Share")}</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(async function onSubmit(values: z.infer<typeof formSchema>) {
              setIsSharing(true);
              await tf(
                t,
                onShare,
                videoId,
                platforms.filter((x, i) => values.platforms[i]).map((x) => x.name),
                postNow === true ? undefined : new Date(values.scheduleAt).toISOString(),
              );
              setIsSharing(false);
            })}
            className="space-y-4"
          >
            {platforms.map((platform) => (
              <CheckboxField
                key={platform.name}
                name={`platforms.${platforms.indexOf(platform)}`}
                label={
                  <>
                    {<platform.icon style={{ color: platform.color }} />} {platform.name}
                  </>
                }
                disabled={!accountsMap.has(platform.name.toLowerCase())}
              />
            ))}
            <label className="flex gap-2">
              <Checkbox checked={postNow} onCheckedChange={setPostNow} /> {t("Post now")}
            </label>
            <DateTimeField name="scheduleAt" label="Schedule at" disabled={postNow === true} />
            <Button type="submit" disabled={isSharing} loading={isSharing}>
              {t("Share")}
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
