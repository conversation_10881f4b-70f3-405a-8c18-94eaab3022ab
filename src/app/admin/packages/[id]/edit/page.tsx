import * as packageQueries from "@/database/drizzle/queries/packages";
import { getDataContext } from "@/lib/getDataContext";
import { notFound, redirect } from "next/navigation";
import { PackageEditForm } from "./PackageEditForm";

interface Props {
  params: Promise<{ id: string }>;
}

export default async function PackageEditPage({ params }: Props) {
  const { id } = await params;
  const { db, session } = await getDataContext();

  if (!session?.user) {
    redirect("/login");
  }

  if (session.user.role !== "admin") {
    redirect("/");
  }

  const packageData = await packageQueries.getPackageById(db, id);

  if (!packageData) {
    notFound();
  }

  return (
    <div className="container mx-auto py-6">
      <PackageEditForm packageData={packageData} />
    </div>
  );
}
