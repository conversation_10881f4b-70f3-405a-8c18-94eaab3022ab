import T from "@/components/T";
import { FeatureItem } from "@/database/drizzle/schema/packages";
import { featureSchema } from "@/lib/features";

export function FeatureDetails({ feature: _feature }: { feature: FeatureItem }) {
  const feature = featureSchema.safeParse(_feature).data;

  if (!feature) return null;

  switch (feature.type) {
    case "24/7 priority support":
      const supportMeta = feature.meta;
      return (
        <div className="text-sm text-muted-foreground ml-4">
          {supportMeta.responseTime && (
            <div>
              <T text="Response time: {time} hours" options={{ time: supportMeta.responseTime }} />
            </div>
          )}
          {supportMeta.channels && supportMeta.channels.length > 0 && (
            <div>
              <T text="Channels: {channels}" options={{ channels: supportMeta.channels.join(", ") }} />
            </div>
          )}
        </div>
      );
    case "Team collaboration":
      const teamMeta = feature.meta;
      return (
        <div className="text-sm text-muted-foreground ml-4">
          {teamMeta.maxMembers && (
            <div>
              <T text="Max members: {count}" options={{ count: teamMeta.maxMembers }} />
            </div>
          )}
          {teamMeta.roles && teamMeta.roles.length > 0 && (
            <div>
              <T text="Roles: {roles}" options={{ roles: teamMeta.roles.join(", ") }} />
            </div>
          )}
        </div>
      );
    case "API access":
      const apiMeta = feature.meta;
      return (
        <div className="text-sm text-muted-foreground ml-4">
          {apiMeta.rateLimit && (
            <div>
              <T text="Rate limit: {limit} req/min" options={{ limit: apiMeta.rateLimit }} />
            </div>
          )}
        </div>
      );
    default:
      return null;
  }
}
