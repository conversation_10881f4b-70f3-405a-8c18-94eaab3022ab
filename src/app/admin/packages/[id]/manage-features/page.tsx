import { Heading } from "@/components/Heading";
import T from "@/components/T";
import { Button } from "@/components/ui/button";
import * as featureQueries from "@/database/drizzle/queries/features";
import * as packageQueries from "@/database/drizzle/queries/packages";
import { getDataContext } from "@/lib/getDataContext";
import { ArrowLeftIcon } from "lucide-react";
import Link from "next/link";
import { notFound, redirect } from "next/navigation";
import { FeatureManagement } from "./FeatureManagement";

interface Props {
  params: Promise<{ id: string }>;
}

export default async function ManageFeaturesPage({ params }: Props) {
  const { id } = await params;
  const { db, session } = await getDataContext();

  if (!session?.user) {
    redirect("/login");
  }

  if (session.user.role !== "admin") {
    redirect("/");
  }

  const packageData = await packageQueries.getPackageById(db, id);

  if (!packageData) {
    notFound();
  }

  const features = await featureQueries.getFeaturesByPackageId(db, id);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <Heading
        title={<T text="Manage Features" />}
        description={<T text="Manage features for {packageName}" options={{ packageName: packageData.name }} />}
        actions={
          <Button variant="outline" size="sm" asChild>
            <Link href={`/admin/packages/${id}`}>
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              <T text="Back to Package" />
            </Link>
          </Button>
        }
      />

      <FeatureManagement packageId={id} initialFeatures={features} />
    </div>
  );
}
