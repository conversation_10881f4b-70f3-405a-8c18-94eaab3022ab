"use server";

import * as featureQueries from "@/database/drizzle/queries/features";
import { FeatureInsert } from "@/database/drizzle/schema/packages";
import { errors } from "@/lib/errors";
import { Feature, FeatureType, featureSchema } from "@/lib/features";
import { getDataContext } from "@/lib/getDataContext";
import { revalidatePath } from "next/cache";
import { z } from "zod";

export async function onCreateFeature(packageId: string, data: Feature) {
  const { db, session } = await getDataContext();

  if (!session?.user) return { error: errors.NotAuthenticated };
  if (session.user.role !== "admin") return { error: errors.NotAuthorized };

  try {
    // Validate the feature data using the schema
    const validatedFeature = featureSchema.parse(data);

    // Check if feature already exists for this package
    const existingFeature = await featureQueries.getFeature(db, validatedFeature.type, packageId);

    if (existingFeature.length > 0) {
      return { error: "Feature already exists for this package" };
    }

    const featureData: FeatureInsert = {
      type: validatedFeature.type,
      packageId,
      meta: validatedFeature.meta,
    };

    const [newFeature] = await featureQueries.insertFeature(db, featureData);

    revalidatePath(`/admin/packages/${packageId}`);
    revalidatePath(`/admin/packages/${packageId}/manage-features`);

    return { success: true, feature: newFeature };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { error: error.issues[0].message };
    }
    console.error("Failed to create feature:", error);
    return { error: "Failed to create feature" };
  }
}

export async function onUpdateFeature(packageId: string, type: FeatureType, data: Feature) {
  const { db, session } = await getDataContext();

  if (!session?.user) return { error: errors.NotAuthenticated };
  if (session.user.role !== "admin") return { error: errors.NotAuthorized };

  try {
    // Validate the feature data using the schema
    const validatedFeature = featureSchema.parse(data);

    // Ensure the type matches
    if (validatedFeature.type !== type) {
      return { error: "Feature type cannot be changed" };
    }

    const updateData = {
      meta: validatedFeature.meta,
    };

    const [updatedFeature] = await featureQueries.updateFeature(db, type, packageId, updateData);

    if (!updatedFeature) {
      return { error: "Feature not found" };
    }

    revalidatePath(`/admin/packages/${packageId}`);
    revalidatePath(`/admin/packages/${packageId}/manage-features`);

    return { success: true, feature: updatedFeature };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { error: error.issues[0].message };
    }
    console.error("Failed to update feature:", error);
    return { error: "Failed to update feature" };
  }
}

export async function onDeleteFeature(packageId: string, type: FeatureType) {
  const { db, session } = await getDataContext();

  if (!session?.user) return { error: errors.NotAuthenticated };
  if (session.user.role !== "admin") return { error: errors.NotAuthorized };

  try {
    const [deletedFeature] = await featureQueries.deleteFeature(db, type, packageId);

    if (!deletedFeature) {
      return { error: "Feature not found" };
    }

    revalidatePath(`/admin/packages/${packageId}`);
    revalidatePath(`/admin/packages/${packageId}/manage-features`);

    return { success: true };
  } catch (error) {
    console.error("Failed to delete feature:", error);
    return { error: "Failed to delete feature" };
  }
}
