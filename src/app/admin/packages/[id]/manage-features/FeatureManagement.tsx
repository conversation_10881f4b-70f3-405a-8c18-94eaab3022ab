"use client";

import T from "@/components/T";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { FeatureItem } from "@/database/drizzle/schema/packages";
import { FeatureType } from "@/lib/features";
import { tf } from "@/lib/tf";
import { EditIcon, PlusIcon, TrashIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { toast } from "sonner";
import { FeatureDetails } from "../FeatureDetails";
import { onDeleteFeature } from "./actions";
import { FeatureFormDialog } from "./FeatureFormDialog";

interface FeatureManagementProps {
  packageId: string;
  initialFeatures: FeatureItem[];
}

export function FeatureManagement({ packageId, initialFeatures }: FeatureManagementProps) {
  const t = useTranslations();
  const [features, setFeatures] = useState<FeatureItem[]>(initialFeatures);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingFeature, setEditingFeature] = useState<FeatureItem | undefined>();

  const existingFeatureTypes = features.map((f) => f.type);

  const handleAddFeature = () => {
    setEditingFeature(undefined);
    setIsDialogOpen(true);
  };

  const handleEditFeature = (feature: FeatureItem) => {
    setEditingFeature(feature);
    setIsDialogOpen(true);
  };

  const handleDeleteFeature = async (type: FeatureType) => {
    if (!confirm(t("Are you sure you want to delete this feature?"))) {
      return;
    }

    const result = await tf(t, onDeleteFeature, packageId, type);

    if (result?.success) {
      setFeatures((prev) => prev.filter((f) => f.type !== type));
      toast.success(t("Feature deleted successfully"));
    }
  };

  const handleSuccess = () => {
    // Refresh the features list
    window.location.reload();
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>
                <T text="Package Features" />
              </CardTitle>
              <CardDescription>
                <T text="Manage the features available in this package" />
              </CardDescription>
            </div>
            <Button onClick={handleAddFeature}>
              <PlusIcon className="h-4 w-4 mr-2" />
              <T text="Add Feature" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {features.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <T text="No features configured for this package" />
            </div>
          ) : (
            <div className="space-y-4">
              {features.map((feature) => (
                <div key={feature.type} className="flex items-start justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="font-medium">{t(feature.type)}</h3>
                    </div>
                    <FeatureDetails feature={feature} />
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <Button variant="outline" size="sm" onClick={() => handleEditFeature(feature)}>
                      <EditIcon className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteFeature(feature.type)}
                      className="text-destructive hover:text-destructive"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <FeatureFormDialog
        isOpen={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        packageId={packageId}
        feature={editingFeature}
        key={editingFeature?.type}
        existingFeatureTypes={existingFeatureTypes}
        onSuccess={handleSuccess}
      />
    </div>
  );
}
