"use client";

import { Field } from "@/components/Field";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { Separator } from "@/components/ui/separator";
import { authClient } from "@/lib/auth-client";
import { usePageContext } from "@/lib/page-context";
import { zodResolver } from "@hookform/resolvers/zod";
import { Camera, Save, User, X } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { useForm } from "react-hook-form";
import z from "zod";

const formSchema = z.object({
  name: z.string().nonempty(),
  email: z.email().nonempty(),
});

export default function ProfileUpdateForm() {
  const t = useTranslations();
  const { session } = usePageContext();
  const [userData, setUserData] = useState(session!.user);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: userData.name,
      email: userData.email,
    },
  });

  const handleSaveProfile = async (values: z.infer<typeof formSchema>) => {
    setIsLoading(true);
    try {
      await authClient.updateUser({ name: values.name });
      setUserData({ ...userData, ...values });
      setIsEditing(false);
      console.log("Profile updated:", values);
    } catch (error) {
      console.error("Error updating profile:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelEdit = () => {
    form.reset();
    setIsEditing(false);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>{t("Profile Information")}</CardTitle>
            <CardDescription>{t("Update your personal information and profile details")}</CardDescription>
          </div>
          {!isEditing && (
            <Button onClick={() => setIsEditing(true)} variant="outline">
              {t("Edit Profile")}
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Avatar className="h-20 w-20">
              <AvatarImage src={userData.image || undefined} alt={userData.name} />
              <AvatarFallback>
                <User />
              </AvatarFallback>
            </Avatar>
            {isEditing && (
              <Button size="sm" variant="secondary" className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full p-0">
                <Camera className="h-4 w-4" />
              </Button>
            )}
          </div>
          <div className="space-y-1">
            <h3 className="text-lg font-semibold">{userData.name}</h3>
            <p className="text-sm text-gray-600">{userData.email}</p>
            {/* <Badge variant="secondary">{userData.plan} Plan</Badge> */}
          </div>
        </div>

        <Separator />

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSaveProfile)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Field name="name" label={t("Full Name")} control={form.control} />
              <Field
                name="email"
                label={t("Email Address")}
                control={form.control}
                input={{ props: { type: "email" } }}
              />
            </div>

            {isEditing && (
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={handleCancelEdit}>
                  <X className="h-4 w-4 mr-2" />
                  {t("Cancel")}
                </Button>
                <Button type="submit" disabled={isLoading} loading={isLoading}>
                  <Save className="h-4 w-4 mr-2" />
                  {t("Save Changes")}
                </Button>
              </div>
            )}
          </form>
        </Form>

        <Separator />

        <div className="text-sm text-gray-600">
          <p>
            {t("Member since")} {userData.createdAt.toDateString()}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
