"use client";

import { Heading } from "@/components/Heading";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/tabs";
import { <PERSON>, Settings, Shield, User } from "lucide-react";
import { useTranslations } from "next-intl";

export default function Layout({ children }: { children: React.ReactNode }) {
  const t = useTranslations();
  return (
    <>
      <Heading title={t("Account Settings")} description={t("Manage your account information and preferences")} />

      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger href="/account/profile" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            {t("Profile")}
          </TabsTrigger>
          <TabsTrigger href="/account/security" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            {t("Security")}
          </TabsTrigger>
          <TabsTrigger href="/account/preferences" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            {t("Preferences")}
          </TabsTrigger>
          <TabsTrigger href="/account/social-accounts" className="flex items-center gap-2">
            <Link className="h-4 w-4" />
            {t("Social Accounts")}
          </TabsTrigger>
        </TabsList>

        <TabsContent className="space-y-6">{children}</TabsContent>
      </Tabs>
    </>
  );
}
