"use client";

import { Toaster } from "@/components/ui/sonner";
import type { auth } from "@/lib/auth";
import { PageContext } from "@/lib/page-context";
import { useLocale } from "next-intl";
import { useEffect } from "react";
import z from "zod";
import { en, fr } from "zod/locales";

export default function Provider({
  children,
  session,
}: {
  children: React.ReactNode;
  session: Awaited<ReturnType<typeof auth.api.getSession>>;
}) {
  const locale = useLocale();

  useEffect(() => {
    z.config(locale === "en" ? en() : fr());
  }, [locale]);

  return (
    <>
      <PageContext value={{ session }}>{children}</PageContext>
      <Toaster position="top-center" />
    </>
  );
}
